<template>
  <div :class="[prefixCls]" :style="containerStyle">
    <div :class="`${prefixCls}__grid`" :style="gridStyle">
      <div
        v-for="item in visibleItems"
        :key="item.field"
        :class="[
          `${prefixCls}__item`,
          `${prefixCls}__item--${getThemeClass(item)}`,
          item.className,
        ]"
        :style="[itemStyle, item.style]"
        @click="handleItemClick(item)"
      >
        <!-- 卡片头部 -->
        <div :class="`${prefixCls}__header`">
          <div :class="`${prefixCls}__title`" :style="{ color: item.titleColor }">
            <template v-if="typeof item.title === 'string'">
              {{ item.title }}
            </template>
            <component v-else :is="item.title" />
          </div>

          <!-- 统计单位标签 -->
          <div
            v-if="item.unit"
            :class="`${prefixCls}__unit`"
            :style="{
              backgroundColor: getThemeColor(item),
              color: item.unitColor || 'white',
            }"
          >
            {{ item.unit }}
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div :class="`${prefixCls}__main`">
          <!-- 数值 -->
          <div :class="`${prefixCls}__value`" :style="{ color: item.valueColor }">
            <template v-if="item.render">
              {{ renderContent(item) }}
            </template>
            <template v-else-if="item.useAnimation !== false">
              <CountTo
                :start-val="0"
                :end-val="getNumericValue(item)"
                :duration="item.duration || 1500"
                :decimals="item.decimals || 0"
                :separator="item.separator || ','"
                :decimal="item.decimal || '.'"
                :prefix="item.prefix || ''"
                :suffix="item.suffix || ''"
                :color="item.valueColor || undefined"
              />
            </template>
            <template v-else>
              {{ formatStaticValue(item) }}
            </template>
          </div>

          <!-- 图标区域 -->
          <div v-if="item.icon" :class="`${prefixCls}__icon`">
            <Icon
              v-if="typeof item.icon === 'string' && item.icon"
              :icon="item.icon"
              :color="getThemeColor(item)"
              :size="40"
            />
            <component v-else-if="item.icon" :is="item.icon" />
          </div>
        </div>

        <!-- 底部数据 -->
        <div v-if="item.footer" :class="`${prefixCls}__footer`">
          <span :class="`${prefixCls}__footer-title`">
            {{ item.footer.title || `总${item.title}` }}
          </span>
          <span :class="`${prefixCls}__footer-value`" :style="{ color: item.footer.color }">
            <template v-if="item.footer.render">
              {{ renderFooterContent(item) }}
            </template>
            <template v-else-if="item.useAnimation !== false">
              <CountTo
                :start-val="0"
                :end-val="getFooterValue(item)"
                :duration="(item.duration || 1500) + 200"
                :decimals="0"
                :separator="item.separator || ','"
                :prefix="item.footer.prefix || ''"
                :suffix="item.footer.suffix || ''"
                :color="item.footer.color"
              />
            </template>
            <template v-else>
              {{ formatFooterValue(item) }}
            </template>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="tsx">
  import { defineComponent, computed, ref, unref, type PropType, type CSSProperties } from 'vue';
  import { useDesign } from '@/hooks/web/useDesign';
  import Icon from '@/components/Icon/Icon.vue';
  import { CountTo } from '@/components/CountTo';
  import { isFunction, isNumber } from '@/utils/is';
  import { get } from 'lodash-es';
  import type { StatisticItem, StatisticCardProps, StatisticCardInstance } from './typing';

  const props = {
    schema: {
      type: Array as PropType<StatisticItem[]>,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    column: {
      type: [Number, Object],
      default: () => ({ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }),
    },
    gap: {
      type: [Number, String],
      default: 16,
    },
    bordered: {
      type: Boolean,
      default: true,
    },
    borderRadius: {
      type: [Number, String],
      default: 8,
    },
    shadow: {
      type: String,
      default: '0 2px 8px rgba(0, 0, 0, 0.1)',
    },
    backgroundColor: {
      type: String,
      default: '#ffffff',
    },
    padding: {
      type: [Number, String],
      default: 20,
    },
    minHeight: {
      type: [Number, String],
      default: 120,
    },
  };

  export default defineComponent({
    name: 'StatisticCard',
    components: { Icon, CountTo },
    props,
    emits: ['register'],
    setup(props, { emit }) {
      const { prefixCls } = useDesign('statistic-card');
      const propsRef = ref<Partial<StatisticCardProps> | null>(null);

      const getMergeProps = computed(() => {
        return {
          ...props,
          ...(unref(propsRef) as any),
        } as StatisticCardProps;
      });

      const visibleItems = computed(() => {
        const { schema, data } = unref(getMergeProps);
        return schema.filter((item) => {
          if (item.show && isFunction(item.show)) {
            return item.show(data);
          }
          return true;
        });
      });

      const containerStyle = computed((): CSSProperties => {
        return {
          padding: `${props.padding}px`,
        };
      });

      const gridStyle = computed((): CSSProperties => {
        const { column, gap } = unref(getMergeProps);
        let cols = 4;

        if (isNumber(column)) {
          cols = column;
        } else if (typeof column === 'object') {
          // 简化响应式处理，这里可以根据需要扩展
          cols = column.lg || 3;
        }

        return {
          display: 'grid',
          gridTemplateColumns: `repeat(${cols}, 1fr)`,
          gap: `${gap}px`,
        };
      });

      const itemStyle = computed((): CSSProperties => {
        const { bordered, borderRadius, shadow, backgroundColor, minHeight } = unref(getMergeProps);

        return {
          backgroundColor,
          borderRadius: `${borderRadius}px`,
          boxShadow: shadow,
          border: bordered ? '1px solid #f0f0f0' : 'none',
          padding: '20px',
          minHeight: `${minHeight}px`,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
        };
      });

      function getNumericValue(item: StatisticItem): number {
        const { data } = unref(getMergeProps);
        let value = item.value;

        if (typeof value === 'string' && data) {
          value = get(data, item.field, value);
        }

        // 确保返回有效的数字
        if (isNumber(value)) {
          return value;
        }

        const parsed = parseFloat(String(value));
        return isNaN(parsed) ? 0 : parsed;
      }

      function formatStaticValue(item: StatisticItem): string {
        const value = getNumericValue(item);
        const { prefix = '', suffix = '', separator = ',', decimal = '.', decimals = 0 } = item;

        let formattedValue = value.toFixed(decimals);
        const parts = formattedValue.split('.');
        let integerPart = parts[0];
        const decimalPart = parts.length > 1 ? decimal + parts[1] : '';

        // 添加千分位分隔符
        const rgx = /(\d+)(\d{3})/;
        while (rgx.test(integerPart)) {
          integerPart = integerPart.replace(rgx, '$1' + separator + '$2');
        }

        return prefix + integerPart + decimalPart + suffix;
      }

      function renderContent(item: StatisticItem): string {
        const { data } = unref(getMergeProps);
        const value = getNumericValue(item);

        if (item.render && isFunction(item.render)) {
          const result = item.render(value, data || {});
          // 确保返回字符串
          return String(result);
        }

        return formatStaticValue(item);
      }

      function handleItemClick(item: StatisticItem) {
        const { data } = unref(getMergeProps);
        if (item.onClick && isFunction(item.onClick)) {
          item.onClick(item, data || {});
        }
      }

      function getIconBackground(color?: string): string {
        if (!color) {
          return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }

        // 根据颜色生成渐变背景
        const colorMap: Record<string, string> = {
          '#1890ff': 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
          '#52c41a': 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
          '#faad14': 'linear-gradient(135deg, #faad14 0%, #d46b08 100%)',
          '#f5222d': 'linear-gradient(135deg, #f5222d 0%, #cf1322 100%)',
          '#722ed1': 'linear-gradient(135deg, #722ed1 0%, #531dab 100%)',
          '#13c2c2': 'linear-gradient(135deg, #13c2c2 0%, #08979c 100%)',
        };

        return colorMap[color] || `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`;
      }

      function getThemeColor(item: StatisticItem): string {
        if (item.iconColor) {
          return item.iconColor;
        }

        const themeColorMap: Record<string, string> = {
          blue: '#1890ff',
          green: '#52c41a',
          orange: '#faad14',
          red: '#f5222d',
          purple: '#722ed1',
          cyan: '#13c2c2',
        };

        return themeColorMap[item.theme || 'blue'] || item.theme || '#1890ff';
      }

      function getThemeClass(item: StatisticItem): string {
        return item.theme || 'blue';
      }

      function getFooterValue(item: StatisticItem): number {
        const { data } = unref(getMergeProps);
        let value = item.footer?.value;

        if (typeof value === 'string' && data) {
          value = get(data, item.footer?.field || value, value);
        }

        if (isNumber(value)) {
          return value;
        }

        const parsed = parseFloat(String(value));
        return isNaN(parsed) ? 0 : parsed;
      }

      function formatFooterValue(item: StatisticItem): string {
        const value = getFooterValue(item);
        const { prefix = '', suffix = '', separator = ',', decimal = '.' } = item.footer || {};

        let formattedValue = value.toFixed(0);
        const parts = formattedValue.split('.');
        let integerPart = parts[0];
        const decimalPart = parts.length > 1 ? decimal + parts[1] : '';

        // 添加千分位分隔符
        const rgx = /(\d+)(\d{3})/;
        while (rgx.test(integerPart)) {
          integerPart = integerPart.replace(rgx, '$1' + separator + '$2');
        }

        return prefix + integerPart + decimalPart + suffix;
      }

      function renderFooterContent(item: StatisticItem): string {
        const { data } = unref(getMergeProps);
        const value = getFooterValue(item);

        if (item.footer?.render && isFunction(item.footer.render)) {
          const result = item.footer.render(value, data || {});
          return String(result);
        }

        return formatFooterValue(item);
      }

      function setStatisticProps(statisticProps: Partial<StatisticCardProps>): void {
        propsRef.value = {
          ...(unref(propsRef) as Record<string, any>),
          ...statisticProps,
        } as Record<string, any>;
      }

      const methods: StatisticCardInstance = {
        setStatisticProps,
      };

      emit('register', methods);

      return {
        prefixCls,
        visibleItems,
        containerStyle,
        gridStyle,
        itemStyle,
        getNumericValue,
        formatStaticValue,
        renderContent,
        handleItemClick,
        getIconBackground,
        getThemeColor,
        getThemeClass,
        getFooterValue,
        formatFooterValue,
        renderFooterContent,
      };
    },
  });
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 768px) {
    .@{prefix-cls} {
      &__header {
        margin-bottom: 12px;
        padding: 12px 16px 0;
      }

      &__title {
        font-size: 14px;
      }

      &__unit {
        padding: 3px 6px;
        font-size: 11px;
      }

      &__main {
        padding: 0 16px 12px;
      }

      &__value {
        font-size: 24px;
      }

      &__footer {
        padding: 10px 16px;
      }

      &__footer-title,
      &__footer-value {
        font-size: 13px;
      }
    }
  }

  @media (max-width: 480px) {
    .@{prefix-cls} {
      &__header {
        margin-bottom: 10px;
        padding: 10px 12px 0;
      }

      &__title {
        font-size: 13px;
      }

      &__main {
        padding: 0 12px 10px;
      }

      &__value {
        font-size: 20px;
      }

      &__footer {
        padding: 8px 12px;
      }

      &__footer-title,
      &__footer-value {
        font-size: 12px;
      }
    }
  }

  .@{prefix-cls} {
    &__grid {
      width: 100%;
    }

    &__item {
      position: relative;
      padding: 0;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      background: #fff;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgb(0 0 0 / 12%);
      }

      // 主题色边框
      &--blue {
        border-top: 3px solid #1890ff;
      }

      &--green {
        border-top: 3px solid #52c41a;
      }

      &--orange {
        border-top: 3px solid #faad14;
      }

      &--red {
        border-top: 3px solid #f5222d;
      }

      &--purple {
        border-top: 3px solid #722ed1;
      }

      &--cyan {
        border-top: 3px solid #13c2c2;
      }
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 16px 20px 0;
    }

    &__title {
      margin: 0;
      color: #262626;
      font-size: 16px;
      font-weight: 600;
    }

    &__unit {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      line-height: 1;
    }

    &__main {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px 16px;
    }

    &__value {
      color: #262626;
      font-size: 30px;
      font-weight: 700;
      line-height: 1.2;
    }

    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      opacity: 0.8;

      &:hover {
        transform: scale(1.1);
        opacity: 1;
      }
    }

    &__footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 20px;
      border-top: 1px solid #f0f0f0;
      background: #fafafa;
    }

    &__footer-title {
      color: #8c8c8c;
      font-size: 14px;
      font-weight: 400;
    }

    &__footer-value {
      color: #262626;
      font-size: 14px;
      font-weight: 600;
    }
  }
  @prefix-cls: ~'@{namespace}-statistic-card';
</style>
