<template>
  <div class="online-i18n-demo">
    <h2>在线 i18n Hook 演示</h2>

    <!-- 单个翻译示例 -->
    <div class="demo-section">
      <h3>单个翻译示例</h3>
      <div class="demo-item">
        <label>页面标题:</label>
        <span v-if="!titleLoading" class="translation-text">{{ title }}</span>
        <span v-else class="loading">加载中...</span>
        <button @click="refreshTitle" class="refresh-btn">刷新</button>
      </div>
      <div v-if="titleError" class="error">错误: {{ titleError }}</div>
      <div class="demo-item">
        <label>页面标题:</label>
        <span class="translation-text">{{ t('DEVICE_TEST', '测试设备') }}</span>
      </div>
    </div>

    <!-- 批量翻译示例 -->
    <div class="demo-section">
      <h3>批量翻译示例</h3>
      <div v-if="!batchLoading" class="demo-item">
        <button class="demo-btn">{{ saveText }}</button>
        <button class="demo-btn">{{ cancelText }}</button>
        <button class="demo-btn">{{ deleteText }}</button>
      </div>
      <div v-else class="loading">批量翻译加载中...</div>
      <div v-if="batchError" class="error">错误: {{ batchError }}</div>
    </div>

    <!-- 表单翻译示例 -->
    <div class="demo-section">
      <h3>表单翻译示例</h3>
      <form v-if="!formLoading" class="demo-form">
        <div class="form-item">
          <label>{{ nameLabel }}:</label>
          <input v-model="form.name" type="text" />
        </div>
        <div class="form-item">
          <label>{{ emailLabel }}:</label>
          <input v-model="form.email" type="email" />
        </div>
        <div class="form-item">
          <label>{{ phoneLabel }}:</label>
          <input v-model="form.phone" type="tel" />
        </div>
        <div class="form-actions">
          <button type="button" @click="handleSave" class="save-btn">
            {{ formSaveText }}
          </button>
          <button type="button" @click="handleReset" class="reset-btn">
            {{ formResetText }}
          </button>
        </div>
      </form>
      <div v-else class="loading">表单翻译加载中...</div>
    </div>

    <!-- 翻译函数示例 -->
    <div class="demo-section">
      <h3>翻译函数示例</h3>
      <div class="demo-item">
        <button @click="showAsyncMessage" class="demo-btn">显示异步消息</button>
        <button @click="showSyncMessage" class="demo-btn">显示同步消息</button>
      </div>
      <div v-if="message" class="message">{{ message }}</div>
    </div>

    <!-- 动态翻译示例 -->
    <div class="demo-section">
      <h3>动态翻译示例</h3>
      <div class="demo-item">
        <label>选择模块:</label>
        <select v-model="selectedModule" @change="switchModule">
          <option value="user">用户模块</option>
          <option value="product">产品模块</option>
          <option value="order">订单模块</option>
        </select>
      </div>
      <div v-if="!dynamicLoading" class="dynamic-translations">
        <p
          ><strong>标题:</strong>
          {{ dynamicTranslations[`${selectedModule}.title`] || '未找到翻译' }}</p
        >
        <p
          ><strong>描述:</strong>
          {{ dynamicTranslations[`${selectedModule}.description`] || '未找到翻译' }}</p
        >
      </div>
      <div v-else class="loading">动态翻译加载中...</div>
    </div>

    <!-- 预加载示例 -->
    <div class="demo-section">
      <h3>预加载示例</h3>
      <div class="demo-item">
        <button @click="triggerPreload" class="demo-btn">触发预加载</button>
        <span v-if="preloadStatus" class="status">{{ preloadStatus }}</span>
      </div>
    </div>

    <!-- StatisticCard 组件测试 -->
    <div class="demo-section">
      <h3>StatisticCard 统计卡片组件测试</h3>

      <!-- 简单测试 -->
      <div class="statistic-demo">
        <h4>简单测试</h4>
        <StatisticCard :schema="simpleStatisticSchema" :data="statisticData" />
      </div>

      <!-- 基础用法 -->
      <div class="statistic-demo">
        <h4>基础用法</h4>
        <StatisticCard :schema="basicStatisticSchema" :data="statisticData" />
      </div>

      <!-- 自定义样式 -->
      <div class="statistic-demo">
        <h4>自定义样式</h4>
        <StatisticCard
          :schema="customStatisticSchema"
          :data="statisticData"
          :column="3"
          :gap="20"
          border-radius="12"
          shadow="0 4px 20px rgba(0, 0, 0, 0.12)"
          background-color="#f8fafc"
        />
      </div>

      <!-- Hook 用法 -->
      <div class="statistic-demo">
        <h4>Hook 用法 - 动态控制</h4>
        <div class="demo-item">
          <button @click="refreshStatisticData" class="demo-btn">刷新数据</button>
          <button @click="toggleStatisticAnimation" class="demo-btn">
            {{ statisticAnimationEnabled ? '关闭' : '开启' }}动画
          </button>
          <button @click="changeStatisticLayout" class="demo-btn">切换布局</button>
        </div>
        <StatisticCard @register="registerStatistic" />
      </div>

      <!-- 响应式布局 -->
      <div class="statistic-demo">
        <h4>响应式布局</h4>
        <StatisticCard
          :schema="responsiveStatisticSchema"
          :data="statisticData"
          :column="{ xxl: 5, xl: 4, lg: 3, md: 2, sm: 2, xs: 1 }"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, reactive } from 'vue';
  import {
    useOnlineI18n,
    useOnlineI18nBatch,
    useOnlineI18nFunction,
    useOnlineI18nPreload,
  } from '@/hooks/web/useOnlineI18n';
  import { StatisticCard, useStatisticCard } from '@/components/Custom';
  import type { StatisticItem } from '@/components/Custom/StatisticCard';

  // 单个翻译
  const {
    t: title,
    loading: titleLoading,
    error: titleError,
    refresh: refreshTitle,
  } = useOnlineI18n('THIRD', '演示页面标题');

  // 页面单独使用
  const { t } = useOnlineI18n();

  // 批量翻译 - 按钮
  const buttonCodes = ['SAVE', 'DEVICE_NETWORK_PORT', 'button.delete'];
  const {
    loading: batchLoading,
    error: batchError,
    t: getButtonText,
  } = useOnlineI18nBatch(buttonCodes);

  const saveText = getButtonText('SAVE', '保存');
  const cancelText = getButtonText('DEVICE_NETWORK_PORT', 'hahha');
  const deleteText = getButtonText('button.delete', '删除');

  // 批量翻译 - 表单
  const formCodes = [
    { code: 'form.name', defaultValue: '姓名' },
    { code: 'form.email', defaultValue: '邮箱' },
    { code: 'form.phone', defaultValue: '电话' },
    { code: 'form.save', defaultValue: '保存' },
    { code: 'form.reset', defaultValue: '重置' },
  ];

  const { loading: formLoading, t: getFormLabel } = useOnlineI18nBatch(formCodes);

  const nameLabel = getFormLabel('form.name');
  const emailLabel = getFormLabel('form.email');
  const phoneLabel = getFormLabel('form.phone');
  const formSaveText = getFormLabel('form.save');
  const formResetText = getFormLabel('form.reset');

  // 表单数据
  const form = ref({
    name: '',
    email: '',
    phone: '',
  });

  // 翻译函数
  const { t: getMessage, tSync: getMessageSync } = useOnlineI18nFunction();

  // 消息显示
  const message = ref('');

  // 动态翻译
  const selectedModule = ref('user');
  const dynamicCodes = computed(() => [
    `${selectedModule.value}.title`,
    `${selectedModule.value}.description`,
  ]);

  const { translations: dynamicTranslations, loading: dynamicLoading } =
    useOnlineI18nBatch(dynamicCodes);

  // 预加载
  const preloadCodes = ['preload.message1', 'preload.message2', 'preload.message3', 'BIND'];

  const { preload } = useOnlineI18nPreload(preloadCodes);
  const preloadStatus = ref('');

  // 方法
  const handleSave = async () => {
    try {
      console.log('保存表单数据:', form.value);
      const successMessage = await getMessage('message.save.success', '保存成功');
      message.value = successMessage;
      setTimeout(() => {
        message.value = '';
      }, 3000);
    } catch (error) {
      const errorMessage = await getMessage('message.save.error', '保存失败');
      message.value = errorMessage;
      setTimeout(() => {
        message.value = '';
      }, 3000);
    }
  };

  const handleReset = () => {
    form.value = { name: '', email: '', phone: '' };
    const resetMessage = getMessageSync('message.form.reset', '表单已重置');
    message.value = resetMessage;
    setTimeout(() => {
      message.value = '';
    }, 3000);
  };

  const showAsyncMessage = async () => {
    const asyncMessage = await getMessage('message.async.demo', '这是异步获取的消息');
    message.value = `异步: ${asyncMessage}`;
    setTimeout(() => {
      message.value = '';
    }, 3000);
  };

  const showSyncMessage = () => {
    const syncMessage = getMessageSync('message.sync.demo', '这是同步获取的消息');
    message.value = `同步: ${syncMessage}`;
    setTimeout(() => {
      message.value = '';
    }, 3000);
  };

  const switchModule = () => {
    console.log('切换到模块:', selectedModule.value);
  };

  const triggerPreload = async () => {
    preloadStatus.value = '预加载中...';
    try {
      await preload();
      preloadStatus.value = '预加载完成';
    } catch (error) {
      preloadStatus.value = '预加载失败';
    }
    setTimeout(() => {
      preloadStatus.value = '';
    }, 3000);
  };

  // ===== StatisticCard 组件测试数据 =====

  // 统计数据
  const statisticData = reactive({
    totalUsers: 1000,
    activeUsers: 750,
    newUsers: 100,
    revenue: 50000,
    orders: 2580,
    conversion: 3.2,
    growth: 15.8,
    satisfaction: 98.5,
    downloads: 12580,
    views: 89650,
  });

  // 简单配置（用于测试）
  const simpleStatisticSchema: StatisticItem[] = [
    {
      field: 'totalUsers',
      title: '总用户数',
      value: 'totalUsers',
      valueColor: '#1890ff',
    },
    {
      field: 'revenue',
      title: '总收入',
      value: 'revenue',
      valueColor: '#f5222d',
      prefix: '¥',
      decimals: 0,
    },
  ];

  // 基础配置
  const basicStatisticSchema: StatisticItem[] = [
    {
      field: 'totalUsers',
      title: '总用户数',
      value: 'totalUsers',
      icon: 'ant-design:user-outlined',
      iconColor: '#1890ff',
      valueColor: '#1890ff',
    },
    {
      field: 'activeUsers',
      title: '活跃用户',
      value: 'activeUsers',
      icon: 'ant-design:team-outlined',
      iconColor: '#52c41a',
      valueColor: '#52c41a',
    },
    {
      field: 'newUsers',
      title: '新增用户',
      value: 'newUsers',
      icon: 'ant-design:user-add-outlined',
      iconColor: '#faad14',
      valueColor: '#faad14',
    },
    {
      field: 'revenue',
      title: '总收入',
      value: 'revenue',
      icon: 'ant-design:dollar-outlined',
      iconColor: '#f5222d',
      valueColor: '#f5222d',
      prefix: '¥',
      decimals: 2,
    },
  ];

  // 自定义样式配置
  const customStatisticSchema: StatisticItem[] = [
    {
      field: 'orders',
      title: '订单总数',
      value: 'orders',
      icon: 'ant-design:shopping-cart-outlined',
      iconColor: '#722ed1',
      valueColor: '#722ed1',
      duration: 2000,
    },
    {
      field: 'conversion',
      title: '转化率',
      value: 'conversion',
      icon: 'ant-design:rise-outlined',
      iconColor: '#13c2c2',
      valueColor: '#13c2c2',
      suffix: '%',
      decimals: 1,
      duration: 2500,
    },
    {
      field: 'growth',
      title: '增长率',
      value: 'growth',
      icon: 'ant-design:arrow-up-outlined',
      iconColor: '#52c41a',
      valueColor: '#52c41a',
      suffix: '%',
      decimals: 1,
      duration: 1800,
    },
  ];

  // 响应式配置
  const responsiveStatisticSchema: StatisticItem[] = [
    {
      field: 'totalUsers',
      title: '用户',
      value: 'totalUsers',
      icon: 'ant-design:user-outlined',
      iconColor: '#1890ff',
      valueColor: '#1890ff',
    },
    {
      field: 'orders',
      title: '订单',
      value: 'orders',
      icon: 'ant-design:shopping-outlined',
      iconColor: '#722ed1',
      valueColor: '#722ed1',
    },
    {
      field: 'revenue',
      title: '收入',
      value: 'revenue',
      icon: 'ant-design:dollar-outlined',
      iconColor: '#f5222d',
      valueColor: '#f5222d',
      prefix: '¥',
      render: (val: number) => `${(val / 10000).toFixed(1)}万`,
    },
    {
      field: 'conversion',
      title: '转化',
      value: 'conversion',
      icon: 'ant-design:percentage-outlined',
      iconColor: '#13c2c2',
      valueColor: '#13c2c2',
      suffix: '%',
      decimals: 1,
    },
    {
      field: 'satisfaction',
      title: '满意度',
      value: 'satisfaction',
      icon: 'ant-design:smile-outlined',
      iconColor: '#52c41a',
      valueColor: '#52c41a',
      suffix: '%',
      decimals: 1,
    },
  ];

  // Hook 使用
  const [registerStatistic, { setStatisticProps }] = useStatisticCard();
  const statisticAnimationEnabled = ref(true);
  const currentStatisticLayout = ref(4);

  // 初始化 Hook 配置
  setStatisticProps({
    schema: basicStatisticSchema,
    data: statisticData,
    column: currentStatisticLayout.value,
  });

  // StatisticCard 相关方法
  const refreshStatisticData = () => {
    Object.assign(statisticData, {
      totalUsers: Math.floor(Math.random() * 5000) + 1000,
      activeUsers: Math.floor(Math.random() * 3000) + 500,
      newUsers: Math.floor(Math.random() * 500) + 50,
      revenue: Math.floor(Math.random() * 100000) + 20000,
      orders: Math.floor(Math.random() * 5000) + 1000,
      conversion: Math.random() * 10 + 1,
      growth: Math.random() * 30 + 5,
      satisfaction: Math.random() * 10 + 90,
      downloads: Math.floor(Math.random() * 50000) + 10000,
      views: Math.floor(Math.random() * 200000) + 50000,
    });

    setStatisticProps({
      data: statisticData,
    });
  };

  const toggleStatisticAnimation = () => {
    statisticAnimationEnabled.value = !statisticAnimationEnabled.value;

    const updatedSchema = basicStatisticSchema.map((item) => ({
      ...item,
      useAnimation: statisticAnimationEnabled.value,
    }));

    setStatisticProps({
      schema: updatedSchema,
    });
  };

  const changeStatisticLayout = () => {
    const layouts = [2, 3, 4, 5];
    const currentIndex = layouts.indexOf(currentStatisticLayout.value);
    const nextIndex = (currentIndex + 1) % layouts.length;
    currentStatisticLayout.value = layouts[nextIndex];

    setStatisticProps({
      column: currentStatisticLayout.value,
    });
  };
</script>

<style scoped>
  .online-i18n-demo {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }

  .demo-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fafafa;
  }

  .demo-section h3 {
    margin-top: 0;
    color: #333;
  }

  .demo-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 10px;
  }

  .demo-form {
    max-width: 400px;
  }

  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 10px;
  }

  .form-item label {
    min-width: 80px;
    font-weight: bold;
  }

  .form-item input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
  }

  .demo-btn,
  .refresh-btn,
  .save-btn,
  .reset-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
  }

  .demo-btn {
    background-color: #007bff;
    color: white;
  }

  .refresh-btn {
    background-color: #28a745;
    color: white;
  }

  .save-btn {
    background-color: #007bff;
    color: white;
  }

  .reset-btn {
    background-color: #6c757d;
    color: white;
  }

  .demo-btn:hover,
  .refresh-btn:hover,
  .save-btn:hover,
  .reset-btn:hover {
    opacity: 0.8;
  }

  .translation-text {
    color: #007bff;
    font-weight: bold;
  }

  .loading {
    color: #ffc107;
    font-style: italic;
  }

  .error {
    margin-top: 5px;
    color: #dc3545;
    font-size: 14px;
  }

  .message {
    margin-top: 10px;
    padding: 10px;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    background-color: #d4edda;
    color: #155724;
  }

  .dynamic-translations {
    margin-top: 15px;
    padding: 15px;
    border-radius: 4px;
    background-color: #e9ecef;
  }

  .status {
    color: #28a745;
    font-weight: bold;
  }

  select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  /* StatisticCard 测试样式 */
  .statistic-demo {
    margin-bottom: 30px;
  }

  .statistic-demo h4 {
    margin: 0 0 15px;
    color: #666;
    font-size: 16px;
    font-weight: 600;
  }
</style>
