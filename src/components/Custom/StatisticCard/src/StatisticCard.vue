<template>
  <div :class="[prefixCls]" :style="containerStyle">
    <div :class="`${prefixCls}__grid`" :style="gridStyle">
      <template v-for="(item, index) in visibleItems" :key="item.field">
        <Card
          size="small"
          :loading="loading"
          :title="getCardTitle(item)"
          :class="[`${prefixCls}__card`, item.className, getCardClasses(index)]"
          :style="[getCardStyle(item), item.style]"
          @click="handleItemClick(item)"
        >
          <!-- 单位标签 -->
          <template v-if="item.unit" #extra>
            <Tag :color="getTagColor(item)">{{ item.unit }}</Tag>
          </template>

          <!-- 主要内容：数值和图标 -->
          <div :class="`${prefixCls}__main`">
            <!-- 数值 -->
            <div :class="`${prefixCls}__value`" :style="{ color: item.valueColor }">
              <template v-if="item.render">
                <span :class="`${prefixCls}__count`">{{ renderContent(item) }}</span>
              </template>
              <template v-else-if="item.useAnimation !== false">
                <CountTo
                  :start-val="1"
                  :end-val="getNumericValue(item)"
                  :duration="item.duration || 1500"
                  :decimals="item.decimals || 0"
                  :separator="item.separator || ','"
                  :decimal="item.decimal || '.'"
                  :prefix="item.prefix || ''"
                  :suffix="item.suffix || ''"
                  :color="item.valueColor"
                  :class="`${prefixCls}__count`"
                />
              </template>
              <template v-else>
                <span :class="`${prefixCls}__count`">{{ formatStaticValue(item) }}</span>
              </template>
            </div>

            <!-- 图标 -->
            <div v-if="item.icon" :class="`${prefixCls}__icon`">
              <Icon
                v-if="typeof item.icon === 'string' && item.icon"
                :icon="item.icon"
                :size="40"
              />
              <component v-else-if="item.icon" :is="item.icon" />
            </div>
          </div>

          <!-- 底部统计 -->
          <div v-if="item.footer" :class="`${prefixCls}__footer`">
            <span :class="`${prefixCls}__footer-title`">{{
              item.footer.title || `总${item.title}`
            }}</span>
            <span :class="`${prefixCls}__footer-value`">
              <template v-if="item.footer.render">
                {{ renderFooterContent(item) }}
              </template>
              <template v-else-if="item.useAnimation !== false">
                <CountTo
                  :start-val="1"
                  :end-val="getFooterValue(item)"
                  :duration="(item.duration || 1500) + 200"
                  :decimals="item.footer.decimals || 0"
                  :separator="item.footer.separator || ','"
                  :prefix="item.footer.prefix || ''"
                  :suffix="item.footer.suffix || ''"
                />
              </template>
              <template v-else>
                {{ formatFooterValue(item) }}
              </template>
            </span>
          </div>
        </Card>
      </template>
    </div>
  </div>
</template>

<script lang="tsx">
  import { defineComponent, computed, ref, unref, type PropType, type CSSProperties } from 'vue';
  import { useDesign } from '@/hooks/web/useDesign';
  import Icon from '@/components/Icon/Icon.vue';
  import { CountTo } from '@/components/CountTo';
  import { Card, Tag } from 'ant-design-vue';
  import { isFunction, isNumber } from '@/utils/is';
  import { get } from 'lodash-es';
  import type { StatisticItem, StatisticCardProps, StatisticCardInstance } from './typing';

  const props = {
    schema: {
      type: Array as PropType<StatisticItem[]>,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    column: {
      type: [Number, Object],
      default: () => ({ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }),
    },
    gap: {
      type: [Number, String],
      default: 16,
    },
    bordered: {
      type: Boolean,
      default: true,
    },
    borderRadius: {
      type: [Number, String],
      default: 8,
    },
    shadow: {
      type: String,
      default: '0 2px 8px rgba(0, 0, 0, 0.1)',
    },
    backgroundColor: {
      type: String,
      default: '#ffffff',
    },
    padding: {
      type: [Number, String],
      default: 20,
    },
    minHeight: {
      type: [Number, String],
      default: 120,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  };

  export default defineComponent({
    name: 'StatisticCard',
    components: { Icon, CountTo, Card, Tag },
    props,
    emits: ['register'],
    setup(props, { emit }) {
      const { prefixCls } = useDesign('statistic-card');
      const propsRef = ref<Partial<StatisticCardProps> | null>(null);

      const getMergeProps = computed(() => {
        return {
          ...props,
          ...(unref(propsRef) as any),
        } as StatisticCardProps;
      });

      const visibleItems = computed(() => {
        const { schema, data } = unref(getMergeProps);
        return schema.filter((item) => {
          if (item.show && isFunction(item.show)) {
            return item.show(data);
          }
          return true;
        });
      });

      const containerStyle = computed((): CSSProperties => {
        return {
          padding: `${props.padding}px`,
        };
      });

      const gridStyle = computed((): CSSProperties => {
        const { gap } = unref(getMergeProps);

        return {
          display: 'flex',
          flexWrap: 'wrap',
          gap: `${gap}px`,
        };
      });

      const itemStyle = computed((): CSSProperties => {
        const { bordered, borderRadius, shadow, backgroundColor, minHeight } = unref(getMergeProps);

        return {
          backgroundColor,
          borderRadius: `${borderRadius}px`,
          boxShadow: shadow,
          border: bordered ? '1px solid #f0f0f0' : 'none',
          padding: '20px',
          minHeight: `${minHeight}px`,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
        };
      });

      function getNumericValue(item: StatisticItem): number {
        const { data } = unref(getMergeProps);
        let value = item.value;

        if (typeof value === 'string' && data) {
          value = get(data, item.field, value);
        }

        // 确保返回有效的数字
        if (isNumber(value)) {
          return value;
        }

        const parsed = parseFloat(String(value));
        return isNaN(parsed) ? 0 : parsed;
      }

      function formatStaticValue(item: StatisticItem): string {
        const value = getNumericValue(item);
        const { prefix = '', suffix = '', separator = ',', decimal = '.', decimals = 0 } = item;

        let formattedValue = value.toFixed(decimals);
        const parts = formattedValue.split('.');
        let integerPart = parts[0];
        const decimalPart = parts.length > 1 ? decimal + parts[1] : '';

        // 添加千分位分隔符
        const rgx = /(\d+)(\d{3})/;
        while (rgx.test(integerPart)) {
          integerPart = integerPart.replace(rgx, '$1' + separator + '$2');
        }

        return prefix + integerPart + decimalPart + suffix;
      }

      function renderContent(item: StatisticItem): string {
        const { data } = unref(getMergeProps);
        const value = getNumericValue(item);

        if (item.render && isFunction(item.render)) {
          const result = item.render(value, data || {});
          // 确保返回字符串
          return String(result);
        }

        return formatStaticValue(item);
      }

      function handleItemClick(item: StatisticItem) {
        const { data } = unref(getMergeProps);
        if (item.onClick && isFunction(item.onClick)) {
          item.onClick(item, data || {});
        }
      }

      function getIconBackground(color?: string): string {
        if (!color) {
          return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }

        // 根据颜色生成渐变背景
        const colorMap: Record<string, string> = {
          '#1890ff': 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
          '#52c41a': 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
          '#faad14': 'linear-gradient(135deg, #faad14 0%, #d46b08 100%)',
          '#f5222d': 'linear-gradient(135deg, #f5222d 0%, #cf1322 100%)',
          '#722ed1': 'linear-gradient(135deg, #722ed1 0%, #531dab 100%)',
          '#13c2c2': 'linear-gradient(135deg, #13c2c2 0%, #08979c 100%)',
        };

        return colorMap[color] || `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`;
      }

      function getThemeColor(item: StatisticItem): string {
        if (item.iconColor) {
          return item.iconColor;
        }

        const themeColorMap: Record<string, string> = {
          blue: '#1890ff',
          green: '#52c41a',
          orange: '#faad14',
          red: '#f5222d',
          purple: '#722ed1',
          cyan: '#13c2c2',
        };

        return themeColorMap[item.theme || 'blue'] || item.theme || '#1890ff';
      }

      function getThemeClass(item: StatisticItem): string {
        return item.theme || 'blue';
      }

      function getFooterValue(item: StatisticItem): number {
        const { data } = unref(getMergeProps);
        let value = item.footer?.value;

        if (typeof value === 'string' && data) {
          value = get(data, item.footer?.field || value, value);
        }

        if (isNumber(value)) {
          return value;
        }

        const parsed = parseFloat(String(value));
        return isNaN(parsed) ? 0 : parsed;
      }

      function formatFooterValue(item: StatisticItem): string {
        const value = getFooterValue(item);
        const { prefix = '', suffix = '', separator = ',', decimal = '.' } = item.footer || {};

        let formattedValue = value.toFixed(0);
        const parts = formattedValue.split('.');
        let integerPart = parts[0];
        const decimalPart = parts.length > 1 ? decimal + parts[1] : '';

        // 添加千分位分隔符
        const rgx = /(\d+)(\d{3})/;
        while (rgx.test(integerPart)) {
          integerPart = integerPart.replace(rgx, '$1' + separator + '$2');
        }

        return prefix + integerPart + decimalPart + suffix;
      }

      function renderFooterContent(item: StatisticItem): string {
        const { data } = unref(getMergeProps);
        const value = getFooterValue(item);

        if (item.footer?.render && isFunction(item.footer.render)) {
          const result = item.footer.render(value, data || {});
          return String(result);
        }

        return formatFooterValue(item);
      }

      function getCardTitle(item: StatisticItem): string {
        if (typeof item.title === 'string') {
          return item.title;
        }
        return String(item.title);
      }

      function getCardClasses(index: number): string {
        const { column } = unref(getMergeProps);
        const classes: string[] = [`${prefixCls}__card-item`];

        if (isNumber(column)) {
          // 简单数字，所有断点使用相同列数
          classes.push(`${prefixCls}__card-item--col-${column}`);
        } else if (typeof column === 'object') {
          // 响应式对象，为每个断点添加对应的类
          const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
          breakpoints.forEach((bp) => {
            if (column[bp]) {
              classes.push(`${prefixCls}__card-item--${bp}-${column[bp]}`);
            }
          });

          // 如果没有指定任何断点，使用默认值
          if (!breakpoints.some((bp) => column[bp])) {
            classes.push(`${prefixCls}__card-item--col-4`);
          }
        } else {
          // 默认 4 列
          classes.push(`${prefixCls}__card-item--col-4`);
        }

        // 添加索引类用于间距控制
        if (index > 0) {
          classes.push(`${prefixCls}__card-item--mt`);
        }

        return classes.join(' ');
      }

      function getCardStyle(_item: StatisticItem): CSSProperties {
        return {};
      }

      function getTagColor(item: StatisticItem): string {
        const themeColorMap: Record<string, string> = {
          blue: 'blue',
          green: 'green',
          orange: 'orange',
          red: 'red',
          purple: 'purple',
          cyan: 'cyan',
        };

        return themeColorMap[item.theme || 'blue'] || 'blue';
      }

      function setStatisticProps(statisticProps: Partial<StatisticCardProps>): void {
        propsRef.value = {
          ...(unref(propsRef) as Record<string, any>),
          ...statisticProps,
        } as Record<string, any>;
      }

      const methods: StatisticCardInstance = {
        setStatisticProps,
      };

      emit('register', methods);

      return {
        prefixCls,
        visibleItems,
        containerStyle,
        gridStyle,
        itemStyle,
        getNumericValue,
        formatStaticValue,
        renderContent,
        handleItemClick,
        getIconBackground,
        getThemeColor,
        getThemeClass,
        getFooterValue,
        formatFooterValue,
        renderFooterContent,
        getCardTitle,
        getCardClasses,
        getCardStyle,
        getTagColor,
      };
    },
  });
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-statistic-card';

  .@{prefix-cls} {
    &__grid {
      display: flex;
      flex-wrap: wrap;
    }

    &__card-item {
      // 基础样式
      flex: 1;
      width: 100%; // 默认移动端全宽
      min-width: 0;

      // sm: ≥ 576px
      @media (min-width: 576px) {
        &--sm-1 {
          width: 100%;
        }

        &--sm-2 {
          width: calc(50% - 8px);
        }

        &--sm-3 {
          width: calc(33.333% - 11px);
        }

        &--sm-4 {
          width: calc(25% - 12px);
        }

        &--sm-5 {
          width: calc(20% - 13px);
        }

        &--sm-6 {
          width: calc(16.666% - 13px);
        }
      }

      // md: ≥ 768px
      @media (min-width: 768px) {
        &--md-1 {
          width: 100%;
        }

        &--md-2 {
          width: calc(50% - 8px);
        }

        &--md-3 {
          width: calc(33.333% - 11px);
        }

        &--md-4 {
          width: calc(25% - 12px);
        }

        &--md-5 {
          width: calc(20% - 13px);
        }

        &--md-6 {
          width: calc(16.666% - 13px);
        }
      }

      // lg: ≥ 992px
      @media (min-width: 992px) {
        &--lg-1 {
          width: 100%;
        }

        &--lg-2 {
          width: calc(50% - 8px);
        }

        &--lg-3 {
          width: calc(33.333% - 11px);
        }

        &--lg-4 {
          width: calc(25% - 12px);
        }

        &--lg-5 {
          width: calc(20% - 13px);
        }

        &--lg-6 {
          width: calc(16.666% - 13px);
        }
      }

      // xl: ≥ 1200px
      @media (min-width: 1200px) {
        &--xl-1 {
          width: 100%;
        }

        &--xl-2 {
          width: calc(50% - 8px);
        }

        &--xl-3 {
          width: calc(33.333% - 11px);
        }

        &--xl-4 {
          width: calc(25% - 12px);
        }

        &--xl-5 {
          width: calc(20% - 13px);
        }

        &--xl-6 {
          width: calc(16.666% - 13px);
        }
      }

      // xxl: ≥ 1600px
      @media (min-width: 1600px) {
        &--xxl-1 {
          width: 100%;
        }

        &--xxl-2 {
          width: calc(50% - 8px);
        }

        &--xxl-3 {
          width: calc(33.333% - 11px);
        }

        &--xxl-4 {
          width: calc(25% - 12px);
        }

        &--xxl-5 {
          width: calc(20% - 13px);
        }

        &--xxl-6 {
          width: calc(16.666% - 13px);
        }
      }

      // 简单列数样式（向后兼容）
      &--col-1 {
        width: 100%;
      }

      &--col-2 {
        @media (min-width: 768px) {
          width: calc(50% - 8px);
        }
      }

      &--col-3 {
        @media (min-width: 768px) {
          width: calc(33.333% - 11px);
        }
      }

      &--col-4 {
        @media (min-width: 768px) {
          width: calc(25% - 12px);
        }
      }

      &--col-5 {
        @media (min-width: 768px) {
          width: calc(20% - 13px);
        }
      }

      &--col-6 {
        @media (min-width: 768px) {
          width: calc(16.666% - 13px);
        }
      }

      // 响应式断点样式
      // xs: < 576px (默认全宽)
      &--xs-1 {
        width: 100%;
      }

      &--xs-2 {
        width: calc(50% - 8px);
      }

      &--xs-3 {
        width: calc(33.333% - 11px);
      }

      &--xs-4 {
        width: calc(25% - 12px);
      }

      &--xs-5 {
        width: calc(20% - 13px);
      }

      &--xs-6 {
        width: calc(16.666% - 13px);
      }

      // 移动端间距
      &--mt {
        margin-top: 16px;

        @media (min-width: 768px) {
          margin-top: 0;
        }
      }
    }

    &__main {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // 参考 GrowCard: py-4 px-4 flex justify-between items-center
      padding: 1rem;
    }

    &__count {
      // 参考 GrowCard: text-2xl
      font-size: 1.5rem;
      line-height: 2rem;
    }

    &__footer {
      display: flex;
      justify-content: space-between;
      // 参考 GrowCard: p-2 px-4 flex justify-between
      padding: 0.5rem 1rem;
    }

    &__footer-title {
      color: #8c8c8c;
      font-size: 14px;
      font-weight: 400;
    }

    &__footer-value {
      color: #262626;
      font-size: 14px;
      font-weight: 600;
    }
  }
</style>
