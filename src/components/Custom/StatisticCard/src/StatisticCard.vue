<template>
  <div :class="[prefixCls]" :style="containerStyle">
    <div :class="`${prefixCls}__grid`" :style="gridStyle">
      <div
        v-for="item in visibleItems"
        :key="item.field"
        :class="[`${prefixCls}__item`, item.className]"
        :style="[itemStyle, item.style]"
        @click="handleItemClick(item)"
      >
        <!-- 图标区域 -->
        <div
          v-if="item.icon"
          :class="`${prefixCls}__icon`"
          :style="{ background: getIconBackground(item.iconColor) }"
        >
          <Icon
            v-if="typeof item.icon === 'string' && item.icon"
            :icon="item.icon"
            :color="'white'"
            :size="24"
          />
          <component v-else-if="item.icon" :is="item.icon" />
        </div>

        <!-- 内容区域 -->
        <div :class="`${prefixCls}__content`">
          <!-- 数值 -->
          <div :class="`${prefixCls}__value`" :style="{ color: item.valueColor }">
            <template v-if="item.render">
              {{ renderContent(item) }}
            </template>
            <template v-else-if="item.useAnimation !== false">
              <CountTo
                :start-val="0"
                :end-val="getNumericValue(item)"
                :duration="item.duration || 1500"
                :decimals="item.decimals || 0"
                :separator="item.separator || ','"
                :decimal="item.decimal || '.'"
                :prefix="item.prefix || ''"
                :suffix="item.suffix || ''"
                :color="item.valueColor || undefined"
              />
            </template>
            <template v-else>
              {{ formatStaticValue(item) }}
            </template>
          </div>

          <!-- 标题 -->
          <div :class="`${prefixCls}__title`" :style="{ color: item.titleColor }">
            <template v-if="typeof item.title === 'string'">
              {{ item.title }}
            </template>
            <component v-else :is="item.title" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="tsx">
  import { defineComponent, computed, ref, unref, type PropType, type CSSProperties } from 'vue';
  import { useDesign } from '@/hooks/web/useDesign';
  import Icon from '@/components/Icon/Icon.vue';
  import { CountTo } from '@/components/CountTo';
  import { isFunction, isNumber } from '@/utils/is';
  import { get } from 'lodash-es';
  import type { StatisticItem, StatisticCardProps, StatisticCardInstance } from './typing';

  const props = {
    schema: {
      type: Array as PropType<StatisticItem[]>,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    column: {
      type: [Number, Object],
      default: () => ({ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }),
    },
    gap: {
      type: [Number, String],
      default: 16,
    },
    bordered: {
      type: Boolean,
      default: true,
    },
    borderRadius: {
      type: [Number, String],
      default: 8,
    },
    shadow: {
      type: String,
      default: '0 2px 8px rgba(0, 0, 0, 0.1)',
    },
    backgroundColor: {
      type: String,
      default: '#ffffff',
    },
    padding: {
      type: [Number, String],
      default: 20,
    },
    minHeight: {
      type: [Number, String],
      default: 120,
    },
  };

  export default defineComponent({
    name: 'StatisticCard',
    components: { Icon, CountTo },
    props,
    emits: ['register'],
    setup(props, { emit }) {
      const { prefixCls } = useDesign('statistic-card');
      const propsRef = ref<Partial<StatisticCardProps> | null>(null);

      const getMergeProps = computed(() => {
        return {
          ...props,
          ...(unref(propsRef) as any),
        } as StatisticCardProps;
      });

      const visibleItems = computed(() => {
        const { schema, data } = unref(getMergeProps);
        return schema.filter((item) => {
          if (item.show && isFunction(item.show)) {
            return item.show(data);
          }
          return true;
        });
      });

      const containerStyle = computed((): CSSProperties => {
        return {
          padding: `${props.padding}px`,
        };
      });

      const gridStyle = computed((): CSSProperties => {
        const { column, gap } = unref(getMergeProps);
        let cols = 4;

        if (isNumber(column)) {
          cols = column;
        } else if (typeof column === 'object') {
          // 简化响应式处理，这里可以根据需要扩展
          cols = column.lg || 3;
        }

        return {
          display: 'grid',
          gridTemplateColumns: `repeat(${cols}, 1fr)`,
          gap: `${gap}px`,
        };
      });

      const itemStyle = computed((): CSSProperties => {
        const { bordered, borderRadius, shadow, backgroundColor, minHeight } = unref(getMergeProps);

        return {
          backgroundColor,
          borderRadius: `${borderRadius}px`,
          boxShadow: shadow,
          border: bordered ? '1px solid #f0f0f0' : 'none',
          padding: '20px',
          minHeight: `${minHeight}px`,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
        };
      });

      function getNumericValue(item: StatisticItem): number {
        const { data } = unref(getMergeProps);
        let value = item.value;

        if (typeof value === 'string' && data) {
          value = get(data, item.field, value);
        }

        // 确保返回有效的数字
        if (isNumber(value)) {
          return value;
        }

        const parsed = parseFloat(String(value));
        return isNaN(parsed) ? 0 : parsed;
      }

      function formatStaticValue(item: StatisticItem): string {
        const value = getNumericValue(item);
        const { prefix = '', suffix = '', separator = ',', decimal = '.', decimals = 0 } = item;

        let formattedValue = value.toFixed(decimals);
        const parts = formattedValue.split('.');
        let integerPart = parts[0];
        const decimalPart = parts.length > 1 ? decimal + parts[1] : '';

        // 添加千分位分隔符
        const rgx = /(\d+)(\d{3})/;
        while (rgx.test(integerPart)) {
          integerPart = integerPart.replace(rgx, '$1' + separator + '$2');
        }

        return prefix + integerPart + decimalPart + suffix;
      }

      function renderContent(item: StatisticItem): string {
        const { data } = unref(getMergeProps);
        const value = getNumericValue(item);

        if (item.render && isFunction(item.render)) {
          const result = item.render(value, data || {});
          // 确保返回字符串
          return String(result);
        }

        return formatStaticValue(item);
      }

      function handleItemClick(item: StatisticItem) {
        const { data } = unref(getMergeProps);
        if (item.onClick && isFunction(item.onClick)) {
          item.onClick(item, data || {});
        }
      }

      function getIconBackground(color?: string): string {
        if (!color) {
          return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }

        // 根据颜色生成渐变背景
        const colorMap: Record<string, string> = {
          '#1890ff': 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
          '#52c41a': 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
          '#faad14': 'linear-gradient(135deg, #faad14 0%, #d46b08 100%)',
          '#f5222d': 'linear-gradient(135deg, #f5222d 0%, #cf1322 100%)',
          '#722ed1': 'linear-gradient(135deg, #722ed1 0%, #531dab 100%)',
          '#13c2c2': 'linear-gradient(135deg, #13c2c2 0%, #08979c 100%)',
        };

        return colorMap[color] || `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`;
      }

      function setStatisticProps(statisticProps: Partial<StatisticCardProps>): void {
        propsRef.value = {
          ...(unref(propsRef) as Record<string, any>),
          ...statisticProps,
        } as Record<string, any>;
      }

      const methods: StatisticCardInstance = {
        setStatisticProps,
      };

      emit('register', methods);

      return {
        prefixCls,
        visibleItems,
        containerStyle,
        gridStyle,
        itemStyle,
        getNumericValue,
        formatStaticValue,
        renderContent,
        handleItemClick,
        getIconBackground,
      };
    },
  });
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 768px) {
    .@{prefix-cls} {
      &__item {
        min-height: 120px !important;
      }

      &__content {
        padding: 20px;
        padding-right: 70px;
      }

      &__value {
        font-size: 28px;
      }

      &__icon {
        top: 16px;
        right: 16px;
        width: 40px;
        height: 40px;
      }
    }
  }

  @media (max-width: 480px) {
    .@{prefix-cls} {
      &__content {
        padding: 16px;
        padding-right: 60px;
      }

      &__value {
        font-size: 24px;
      }

      &__icon {
        top: 12px;
        right: 12px;
        width: 36px;
        height: 36px;
      }
    }
  }

  .@{prefix-cls} {
    &__grid {
      width: 100%;
    }

    &__item {
      position: relative;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid #f0f0f0;
      border-radius: 12px;
      background: #fff;
      cursor: pointer;

      &:hover {
        transform: translateY(-4px);
        border-color: #e6f7ff;
        box-shadow: 0 12px 24px rgb(0 0 0 / 10%);
      }
    }

    &__icon {
      display: flex;
      position: absolute;
      top: 20px;
      right: 20px;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      transition: all 0.3s ease;
      border-radius: 12px;
      opacity: 0.9;

      &:hover {
        transform: scale(1.05);
        opacity: 1;
      }
    }

    &__content {
      padding: 24px;
      padding-right: 80px; // 为图标留出空间
    }

    &__value {
      margin-bottom: 8px;
      font-size: 32px;
      font-weight: 700;
      letter-spacing: -0.5px;
      line-height: 1.2;
    }

    &__title {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.4;
    }
  }
  @prefix-cls: ~'@{namespace}-statistic-card';
</style>
